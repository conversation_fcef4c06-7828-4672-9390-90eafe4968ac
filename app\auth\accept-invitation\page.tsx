'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Button from '@/app/components/Button';
import { validateInvitationToken, type InvitationTokenData } from '@/lib/utils/jwt';

// Loading component for Suspense fallback
function LoadingInvitation() {
  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
      <h1 className="text-2xl font-bold mb-6">Loading Invitation</h1>
      <div className="flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    </div>
  );
}

// Main invitation component
function AcceptInvitationContent() {
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [invitationData, setInvitationData] = useState<any>(null);
  const [isVerifying, setIsVerifying] = useState(true);

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const verifyToken = async () => {
      try {
        const token = searchParams.get('token');

        if (!token) {
          setError('Invalid invitation link');
          setIsVerifying(false);
          return;
        }

        // Validate invitation token using shared utility
        console.log('Accept invitation: Validating token...');
        const validationResult = validateInvitationToken(token);

        if (!validationResult.valid) {
          console.error('Accept invitation: Token validation failed:', validationResult.error);
          setError(validationResult.error || 'Invalid or expired invitation link');
          setIsVerifying(false);
          return;
        }

        console.log('Accept invitation: Token validation successful');
        setInvitationData(validationResult.invitationData);
        setIsVerifying(false);
      } catch (error) {
        console.error('Token verification error:', error);
        setError('Failed to verify invitation');
        setIsVerifying(false);
      }
    };

    verifyToken();
  }, [searchParams]);

  const handleAcceptInvitation = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Store invitation data in localStorage for the OAuth callback
      localStorage.setItem('invitation_data', JSON.stringify(invitationData));

      // Get API key from environment variable
      const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

      if (!apiKey) {
        throw new Error('Zerodha API key not found');
      }

      // Create redirect parameters for child OAuth flow
      const redirectParams = encodeURIComponent(JSON.stringify({
        invitation_token: searchParams.get('token'),
        master_id: invitationData.masterId,
        child_email: invitationData.childEmail,
        flow_type: 'child_oauth'
      }));

      // Construct Zerodha auth URL for child user
      const authUrl = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}&redirect_params=${redirectParams}`;

      // Redirect to Zerodha OAuth
      window.location.href = authUrl;
    } catch (err) {
      setError('Failed to initiate OAuth flow. Please try again.');
      console.error('OAuth initiation error:', err);
      setIsLoading(false);
    }
  };

  if (isVerifying) {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-6">Verifying Invitation</h1>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error && !invitationData) {
    return (
      <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold mb-6">Invalid Invitation</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <Link href="/">
          <Button>
            Return to Home
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold text-center mb-6">Accept Invitation</h1>

      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-center">
          You have been invited by <strong>{invitationData?.masterEmail}</strong> to join CopyTrade as a Child account.
        </p>
        <p className="text-sm text-gray-600 mt-2 text-center">
          Email: <strong>{invitationData?.childEmail}</strong>
        </p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="mb-6 p-4 bg-green-50 rounded-lg">
        <h3 className="font-medium text-green-800 mb-2">What happens next?</h3>
        <ol className="text-sm text-green-700 space-y-1">
          <li>1. Click "Accept & Connect to Zerodha" below</li>
          <li>2. You'll be redirected to Zerodha for secure authentication</li>
          <li>3. After successful login, you'll be connected as a child user</li>
          <li>4. Start receiving trade copies from your master automatically</li>
        </ol>
      </div>

      <Button
        onClick={handleAcceptInvitation}
        className="w-full"
        isLoading={isLoading}
      >
        Accept & Connect to Zerodha
      </Button>

      <p className="text-xs text-gray-500 text-center mt-4">
        By accepting this invitation, you agree to connect your Zerodha account for trade copying.
      </p>
    </div>
  );
}

// Export the page component with Suspense
export default function AcceptInvitationPage() {
  return (
    <Suspense fallback={<LoadingInvitation />}>
      <AcceptInvitationContent />
    </Suspense>
  );
}