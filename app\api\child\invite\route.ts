import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import { UserService } from '@/lib/services/userService';
import { sendEmail } from '@/utils/email';
import { getAppUrlFromRequest } from '@/lib/utils/url';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';

// HTML email template function
function generateEmailTemplate(masterEmail: string, childEmail: string, zerodhaAuthUrl: string, isDemo: boolean = false) {
  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CopyTrade Invitation</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
          }

          .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
          }

          .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
          }

          .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
          }

          .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
          }

          .content {
            padding: 40px 30px;
          }

          .content h2 {
            color: #333;
            margin-top: 0;
            font-size: 24px;
          }

          .content p {
            margin-bottom: 20px;
            font-size: 16px;
          }

          .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s ease;
          }

          .cta-button:hover {
            transform: translateY(-2px);
          }

          .divider {
            height: 1px;
            background-color: #e1e5e9;
            margin: 30px 0;
          }

          .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
          }

          .footer p {
            margin: 5px 0;
          }

          .demo-badge {
            background-color: #ffc107;
            color: #212529;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
          }

          .highlight {
            background-color: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
            border-radius: 4px;
          }

          ol {
            padding-left: 20px;
          }

          ol li {
            margin-bottom: 8px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 CopyTrade Invitation</h1>
            <p>You're invited to join as a Child Trader${isDemo ? '<span class="demo-badge">DEMO MODE</span>' : ''}</p>
          </div>
          <div class="content">
            <h2>Hello!</h2>
            <p>You have been invited by <strong>${masterEmail}</strong> to join CopyTrade as a Child account.</p>

            <div class="highlight">
              <p><strong>What is CopyTrade?</strong></p>
              <p>CopyTrade allows you to automatically copy trades from experienced traders (Masters) to your own trading account. When your Master executes a trade, it will be automatically replicated in your account.</p>
            </div>

            <p>To get started and connect your ${isDemo ? 'demo' : 'trading'} account, click the button below:</p>

            <div style="text-align: center;">
              <a href="${zerodhaAuthUrl}" class="cta-button">
                🔗 ${isDemo ? 'Accept Invitation & Connect' : 'Accept Invitation & Connect to Broker'}
              </a>
            </div>

            <div class="divider"></div>

            <p><strong>How it works:</strong></p>
            <ol>
              <li>Click the button above to accept the invitation and connect your ${isDemo ? 'demo' : 'trading'} account</li>
              <li>${isDemo ? 'Join the demo environment instantly' : 'Authorize CopyTrade to access your trading account'}</li>
              <li>Start receiving trade copies automatically</li>
            </ol>

            <p><small>This invitation link will expire in 7 days. For security reasons, please do not share this email.</small></p>
          </div>
          <div class="footer">
            <p>If you did not request this invitation, please ignore this email.</p>
            <p>&copy; ${new Date().getFullYear()} CopyTrade. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;
}

// Send invitation to child user with email functionality
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { childEmail } = await request.json();

    if (!childEmail) {
      return NextResponse.json(
        { error: 'Child email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(childEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Get user from database to get the internal user ID
    const dbUser = await UserService.findBySupabaseId(user.id);
    if (!dbUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    const demoMode = isDemoMode();

    // Check if child is already connected
    const existingChild = await prisma.user.findUnique({
      where: { email: childEmail },
      include: {
        childRelationships: {
          where: {
            masterId: dbUser.id,
            isActive: true,
            deletedAt: null
          }
        }
      }
    });

    if (existingChild && existingChild.childRelationships.length > 0) {
      return NextResponse.json(
        { error: 'Child user is already connected' },
        { status: 409 }
      );
    }

    // Create JWT invitation token
    const JWT_SECRET = process.env.JWT_SECRET;
    if (!JWT_SECRET) {
      return NextResponse.json(
        { error: 'Server configuration error: JWT secret not configured' },
        { status: 500 }
      );
    }

    const token = jwt.sign(
      {
        masterEmail: dbUser.email,
        childEmail,
        masterId: dbUser.id,
        type: 'invitation',
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Create or update pending invitation record
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

    await prisma.pendingInvitation.upsert({
      where: {
        senderId_childEmail: {
          senderId: dbUser.id,
          childEmail: childEmail
        }
      },
      update: {
        sentAt: new Date(),
        expiresAt: expiresAt
      },
      create: {
        senderId: dbUser.id,
        senderEmail: dbUser.email,
        childEmail: childEmail,
        sentAt: new Date(),
        expiresAt: expiresAt
      }
    });

    console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] JWT invitation token created from ${dbUser.email} to ${childEmail}`);

    // Now send the actual email
    try {
      // Get API key from environment variable
      const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

      // Create redirect URL with token
      const appUrl = getAppUrlFromRequest(request);
      const redirectUrl = `${appUrl}/auth/accept-invitation?token=${token}`;

      // For demo mode, use simple demo connection URL
      // For production mode, use Zerodha auth URL
      let zerodhaAuthUrl;
      if (demoMode) {
        // Demo mode: direct connection without Zerodha OAuth
        zerodhaAuthUrl = `${appUrl}/auth/demo-connect?token=${token}`;
      } else {
        // Production mode: Zerodha OAuth flow
        const redirectParams = encodeURIComponent(JSON.stringify({
          invitation_token: token,
          master_id: dbUser.id,
          child_email: childEmail
        }));

        zerodhaAuthUrl = apiKey
          ? `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}&redirect_params=${redirectParams}`
          : redirectUrl;
      }

      // Generate email HTML
      const html = generateEmailTemplate(dbUser.email, childEmail, zerodhaAuthUrl, demoMode);

      // Send the email using Nodemailer
      const emailResult = await sendEmail({
        to: childEmail,
        subject: 'Invitation to join CopyTrade as a Child account',
        html: html
      });

      console.log(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Email sent successfully to:`, childEmail, 'MessageId:', emailResult.messageId);

      return NextResponse.json({
        success: true,
        token,
        message: `Invitation sent to ${childEmail}${demoMode ? ' (Demo Mode)' : ''}`,
        emailSent: true,
        messageId: emailResult.messageId,
        demoMode
      });

    } catch (emailError) {
      console.error(`[${demoMode ? 'DEMO' : 'PROD'} MODE] Error sending email:`, emailError);

      return NextResponse.json({
        success: false,
        message: `Failed to send invitation email to ${childEmail}${demoMode ? ' (Demo Mode)' : ''}`,
        emailSent: false,
        emailError: emailError instanceof Error ? emailError.message : 'Unknown email error',
        demoMode
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error creating invitation:', error);

    return NextResponse.json(
      {
        error: 'Failed to create invitation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
